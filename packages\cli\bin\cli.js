#!/usr/bin/env node

/**
 * CSCS Agent CLI - Compiled entry point
 * This file imports and runs the compiled TypeScript CLI
 */

import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { existsSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Try to import the compiled CLI
const libPath = join(__dirname, '..', 'lib', 'cli.js');
const srcPath = join(__dirname, '..', 'src', 'cli.js');

let cliModule;

if (existsSync(libPath)) {
  // Use compiled version
  cliModule = await import(libPath);
} else if (existsSync(srcPath)) {
  // Fallback to source (for development)
  cliModule = await import(srcPath);
} else {
  console.error('CLI module not found. Please run "npm run build" first.');
  process.exit(1);
}
