#!/usr/bin/env node

import { execSync } from 'child_process';
import { cpSync, existsSync, mkdirSync, readFileSync, readdirSync, statSync, writeFileSync } from 'fs';
import { dirname, join, resolve } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Utility functions for colored output
const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.error(`${colors.red}✗${colors.reset} ${msg}`),
  step: (msg) => console.log(`${colors.cyan}→${colors.reset} ${msg}`),
};

// Get templates directory path
const getTemplatesDir = () => {
  const cliDir = dirname(__dirname);
  return join(cliDir, 'templates');
};

// Get available templates
const getAvailableTemplates = () => {
  const templatesDir = getTemplatesDir();

  if (!existsSync(templatesDir)) {
    log.error(`Templates directory not found: ${templatesDir}`);
    return [];
  }

  try {
    return readdirSync(templatesDir).filter(item => {
      const itemPath = join(templatesDir, item);
      return statSync(itemPath).isDirectory();
    });
  } catch (error) {
    log.error(`Failed to read templates directory: ${error.message}`);
    return [];
  }
};

// Validate template
const validateTemplate = (templateName) => {
  const templatesDir = getTemplatesDir();
  const templatePath = join(templatesDir, templateName);

  if (!existsSync(templatePath)) {
    return { valid: false, error: `Template '${templateName}' not found` };
  }

  const packageJsonPath = join(templatePath, 'package.json');
  if (!existsSync(packageJsonPath)) {
    return { valid: false, error: `Template '${templateName}' is missing package.json` };
  }

  return { valid: true, path: templatePath };
};

// Copy template files
const copyTemplate = (templatePath, targetPath) => {
  try {
    log.step(`Copying template files to ${targetPath}...`);

    // Create target directory if it doesn't exist
    if (!existsSync(targetPath)) {
      mkdirSync(targetPath, { recursive: true });
    }

    // Copy all files from template to target
    cpSync(templatePath, targetPath, {
      recursive: true,
      filter: (src) => {
        // Skip node_modules and other build artifacts
        const relativePath = src.replace(templatePath, '');
        return !relativePath.includes('node_modules') &&
               !relativePath.includes('dist') &&
               !relativePath.includes('.git');
      }
    });

    log.success('Template files copied successfully');
    return true;
  } catch (error) {
    log.error(`Failed to copy template: ${error.message}`);
    return false;
  }
};

// Update package.json with project name
const updatePackageJson = (projectPath, projectName) => {
  const packageJsonPath = join(projectPath, 'package.json');

  try {
    log.step('Updating package.json...');

    const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));
    packageJson.name = projectName;

    writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
    log.success('package.json updated');
    return true;
  } catch (error) {
    log.error(`Failed to update package.json: ${error.message}`);
    return false;
  }
};

// Install dependencies
const installDependencies = (projectPath) => {
  try {
    log.step('Installing dependencies...');

    const originalCwd = process.cwd();
    process.chdir(projectPath);

    // Check if pnpm is available, fallback to npm
    let packageManager = 'npm';
    try {
      execSync('pnpm --version', { stdio: 'ignore' });
      packageManager = 'pnpm';
    } catch {
      try {
        execSync('yarn --version', { stdio: 'ignore' });
        packageManager = 'yarn';
      } catch {
        // Use npm as fallback
      }
    }

    log.info(`Using ${packageManager} to install dependencies...`);
    execSync(`${packageManager} install`, { stdio: 'inherit' });

    process.chdir(originalCwd);
    log.success('Dependencies installed successfully');
    return true;
  } catch (error) {
    log.error(`Failed to install dependencies: ${error.message}`);
    return false;
  }
};

// Main create function
const createProject = (templateName, projectName, targetDir) => {
  log.info(`Creating new project '${projectName}' from template '${templateName}'...`);

  // Validate template
  const templateValidation = validateTemplate(templateName);
  if (!templateValidation.valid) {
    log.error(templateValidation.error);
    return false;
  }

  // Resolve target directory
  const projectPath = resolve(process.cwd(), targetDir || projectName);

  // Check if target directory already exists
  if (existsSync(projectPath)) {
    log.error(`Directory '${projectPath}' already exists`);
    return false;
  }

  // Copy template
  if (!copyTemplate(templateValidation.path, projectPath)) {
    return false;
  }

  // Update package.json
  if (!updatePackageJson(projectPath, projectName)) {
    return false;
  }

  // Install dependencies
  if (!installDependencies(projectPath)) {
    log.warning('Dependencies installation failed, you can install them manually later');
  }

  log.success(`Project '${projectName}' created successfully!`);
  log.info(`\nNext steps:`);
  log.info(`  cd ${projectName}`);
  log.info(`  npm run dev`);

  return true;
};

// Parse command line arguments
const parseArgs = () => {
  const args = process.argv.slice(2);
  const options = {
    create: false,
    template: 'basic',
    name: null,
    dir: null,
    help: false,
    list: false,
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    switch (arg) {
      case '--create':
        options.create = true;
        break;
      case '--template':
      case '-t':
        options.template = args[++i];
        break;
      case '--name':
      case '-n':
        options.name = args[++i];
        break;
      case '--dir':
      case '-d':
        options.dir = args[++i];
        break;
      case '--list':
      case '-l':
        options.list = true;
        break;
      case '--help':
      case '-h':
        options.help = true;
        break;
      default:
        if (!options.name && !arg.startsWith('-')) {
          options.name = arg;
        }
        break;
    }
  }

  return options;
};

// Show help
const showHelp = () => {
  console.log(`
${colors.bright}@cscs-agent/cli${colors.reset} - CSCS Agent Project Generator

${colors.bright}Usage:${colors.reset}
  npx @cscs-agent/cli --create [project-name]
  npx @cscs-agent/cli --create --template <template> --name <project-name>

${colors.bright}Options:${colors.reset}
  --create              Create a new project
  --template, -t        Template to use (default: basic)
  --name, -n            Project name
  --dir, -d             Target directory (default: project name)
  --list, -l            List available templates
  --help, -h            Show this help message

${colors.bright}Examples:${colors.reset}
  npx @cscs-agent/cli --create my-agent-app
  npx @cscs-agent/cli --create --template basic --name my-app
  npx @cscs-agent/cli --list
`);
};

// List available templates
const listTemplates = () => {
  const templates = getAvailableTemplates();

  if (templates.length === 0) {
    log.warning('No templates found');
    return;
  }

  console.log(`\n${colors.bright}Available templates:${colors.reset}`);
  templates.forEach(template => {
    const templatePath = join(getTemplatesDir(), template);
    const packageJsonPath = join(templatePath, 'package.json');

    let description = '';
    if (existsSync(packageJsonPath)) {
      try {
        const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));
        description = packageJson.description || '';
      } catch {
        // Ignore errors reading package.json
      }
    }

    console.log(`  ${colors.green}${template}${colors.reset}${description ? ` - ${description}` : ''}`);
  });
  console.log();
};

// Main execution
const main = () => {
  const options = parseArgs();

  if (options.help) {
    showHelp();
    return;
  }

  if (options.list) {
    listTemplates();
    return;
  }

  if (!options.create) {
    log.error('Missing --create flag');
    showHelp();
    process.exit(1);
  }

  if (!options.name) {
    log.error('Project name is required');
    showHelp();
    process.exit(1);
  }

  // Validate template exists
  const templates = getAvailableTemplates();
  if (!templates.includes(options.template)) {
    log.error(`Template '${options.template}' not found`);
    log.info('Available templates:');
    templates.forEach(t => log.info(`  - ${t}`));
    process.exit(1);
  }

  // Create project
  const success = createProject(options.template, options.name, options.dir);
  process.exit(success ? 0 : 1);
};

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { createProject, getAvailableTemplates, validateTemplate };