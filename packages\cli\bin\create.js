#!/usr/bin/env node

/**
 * Legacy CLI entry point - redirects to new CLI
 * This file maintains backward compatibility with the old CLI interface
 */

import { execSync } from 'child_process';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Colors for output
const colors = {
  reset: '\x1b[0m',
  yellow: '\x1b[33m',
  cyan: '\x1b[36m',
};

console.log(`${colors.yellow}⚠${colors.reset} This CLI interface is deprecated. Please use the new CLI:`);
console.log(`${colors.cyan}npx @cscs-agent/cli create [project-name]${colors.reset}`);
console.log();

// Try to run the new CLI
try {
  const cliPath = join(__dirname, 'cli.js');

  // Map old arguments to new format
  const args = process.argv.slice(2);
  const newArgs = [];

  let i = 0;
  while (i < args.length) {
    const arg = args[i];

    switch (arg) {
      case '--create':
        newArgs.push('create');
        break;
      case '--list':
      case '-l':
        newArgs.push('list');
        break;
      case '--help':
      case '-h':
        newArgs.push('--help');
        break;
      case '--template':
      case '-t':
        newArgs.push('--template', args[++i]);
        break;
      case '--name':
      case '-n':
        newArgs.push(args[++i]); // Project name as positional argument
        break;
      case '--dir':
      case '-d':
        newArgs.push('--directory', args[++i]);
        break;
      default:
        if (!arg.startsWith('-')) {
          newArgs.push(arg);
        }
        break;
    }
    i++;
  }

  // If no command specified, show help
  if (newArgs.length === 0) {
    newArgs.push('--help');
  }

  // Execute new CLI
  execSync(`node "${cliPath.replace(/\\/g, '/')}" ${newArgs.join(' ')}`, { stdio: 'inherit' });

} catch (error) {
  console.error(`Failed to run new CLI: ${error.message}`);
  process.exit(1);
}