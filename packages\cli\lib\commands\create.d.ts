/**
 * Create command implementation
 */
import type { CreateCommandOptions } from '../types.js';
export declare class CreateCommand {
    private templateManager;
    private projectCreator;
    constructor();
    /**
     * Interactive template selection
     */
    private selectTemplate;
    /**
     * Interactive project name input
     */
    private getProjectName;
    /**
     * Interactive package manager selection
     */
    private selectPackageManager;
    /**
     * Interactive mode
     */
    private runInteractive;
    /**
     * Non-interactive mode
     */
    private runNonInteractive;
    /**
     * Execute create command
     */
    execute(projectName?: string, options?: CreateCommandOptions): Promise<void>;
    /**
     * List available templates
     */
    listTemplates(): void;
}
