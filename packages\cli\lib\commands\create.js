/**
 * Create command implementation
 */
import inquirer from 'inquirer';
import chalk from 'chalk';
import { Logger } from '../utils/logger.js';
import { TemplateManager } from '../utils/template-manager.js';
import { ProjectCreator } from '../utils/project-creator.js';
export class CreateCommand {
    templateManager;
    projectCreator;
    constructor() {
        this.templateManager = new TemplateManager();
        this.projectCreator = new ProjectCreator();
    }
    /**
     * Interactive template selection
     */
    async selectTemplate() {
        const templates = this.templateManager.getAvailableTemplates();
        const validTemplates = templates.filter(t => t.valid);
        if (validTemplates.length === 0) {
            Logger.error('No valid templates found');
            process.exit(1);
        }
        if (validTemplates.length === 1) {
            Logger.info(`Using template: ${validTemplates[0].name}`);
            return validTemplates[0].name;
        }
        const choices = validTemplates.map(template => ({
            name: `${chalk.bold(template.name)}${template.description ? ` - ${template.description}` : ''}`,
            value: template.name,
            short: template.name
        }));
        const { selectedTemplate } = await inquirer.prompt([
            {
                type: 'list',
                name: 'selectedTemplate',
                message: 'Select a template:',
                choices,
                pageSize: 10
            }
        ]);
        return selectedTemplate;
    }
    /**
     * Interactive project name input
     */
    async getProjectName() {
        const { projectName } = await inquirer.prompt([
            {
                type: 'input',
                name: 'projectName',
                message: 'Project name:',
                validate: (input) => {
                    const validation = this.projectCreator.validateProjectName(input);
                    return validation.valid || validation.error || 'Invalid project name';
                }
            }
        ]);
        return projectName;
    }
    /**
     * Interactive package manager selection
     */
    async selectPackageManager() {
        const detected = this.projectCreator.detectPackageManager();
        const { useDetected } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'useDetected',
                message: `Use detected package manager (${chalk.cyan(detected)})?`,
                default: true
            }
        ]);
        if (useDetected) {
            return detected;
        }
        const { packageManager } = await inquirer.prompt([
            {
                type: 'list',
                name: 'packageManager',
                message: 'Select package manager:',
                choices: [
                    { name: 'npm', value: 'npm' },
                    { name: 'yarn', value: 'yarn' },
                    { name: 'pnpm', value: 'pnpm' }
                ]
            }
        ]);
        return packageManager;
    }
    /**
     * Interactive mode
     */
    async runInteractive() {
        Logger.title('CSCS Agent Project Creator');
        // Get project name
        const projectName = await this.getProjectName();
        // Select template
        const template = await this.selectTemplate();
        // Select package manager
        const packageManager = await this.selectPackageManager();
        // Confirm creation
        const { confirm } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'confirm',
                message: `Create project '${chalk.cyan(projectName)}' using template '${chalk.cyan(template)}' with ${chalk.cyan(packageManager)}?`,
                default: true
            }
        ]);
        if (!confirm) {
            Logger.info('Project creation cancelled');
            return;
        }
        // Create project
        const success = await this.projectCreator.createProject({
            name: projectName,
            template,
            packageManager: packageManager
        });
        if (!success) {
            process.exit(1);
        }
    }
    /**
     * Non-interactive mode
     */
    async runNonInteractive(projectName, options) {
        // Validate project name
        const nameValidation = this.projectCreator.validateProjectName(projectName);
        if (!nameValidation.valid) {
            Logger.error(nameValidation.error || 'Invalid project name');
            process.exit(1);
        }
        // Get template
        let template = options.template;
        if (!template) {
            template = this.templateManager.getDefaultTemplate();
            Logger.info(`Using default template: ${template}`);
        }
        // Validate template
        const templateValidation = this.templateManager.validateTemplate(template);
        if (!templateValidation.valid) {
            Logger.error(templateValidation.error || 'Invalid template');
            // Show available templates
            Logger.info('Available templates:');
            const templates = this.templateManager.getAvailableTemplates();
            templates.filter(t => t.valid).forEach(t => {
                Logger.info(`  - ${t.name}`);
            });
            process.exit(1);
        }
        // Get package manager
        const packageManager = options.packageManager || this.projectCreator.detectPackageManager();
        // Create project
        const success = await this.projectCreator.createProject({
            name: projectName,
            template,
            directory: options.directory,
            skipInstall: options.skipInstall,
            packageManager: packageManager
        });
        if (!success) {
            process.exit(1);
        }
    }
    /**
     * Execute create command
     */
    async execute(projectName, options = {}) {
        try {
            if (options.interactive || !projectName) {
                await this.runInteractive();
            }
            else {
                await this.runNonInteractive(projectName, options);
            }
        }
        catch (error) {
            Logger.error(`Failed to create project: ${error instanceof Error ? error.message : 'Unknown error'}`);
            process.exit(1);
        }
    }
    /**
     * List available templates
     */
    listTemplates() {
        this.templateManager.listTemplates();
    }
}
