/**
 * Enhanced logging utilities with colors and formatting
 */
export declare class Logger {
    static info(message: string): void;
    static success(message: string): void;
    static warning(message: string): void;
    static error(message: string): void;
    static step(message: string): void;
    static title(message: string): void;
    static subtitle(message: string): void;
    static dim(message: string): void;
    static newLine(): void;
    static table(data: Array<{
        key: string;
        value: string;
    }>): void;
    static list(items: string[], bullet?: string): void;
}
