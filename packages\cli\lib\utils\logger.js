/**
 * Enhanced logging utilities with colors and formatting
 */
import chalk from 'chalk';
export class Logger {
    static info(message) {
        console.log(chalk.blue('ℹ'), message);
    }
    static success(message) {
        console.log(chalk.green('✓'), message);
    }
    static warning(message) {
        console.log(chalk.yellow('⚠'), message);
    }
    static error(message) {
        console.error(chalk.red('✗'), message);
    }
    static step(message) {
        console.log(chalk.cyan('→'), message);
    }
    static title(message) {
        console.log('\n' + chalk.bold.cyan(message) + '\n');
    }
    static subtitle(message) {
        console.log(chalk.bold(message));
    }
    static dim(message) {
        console.log(chalk.dim(message));
    }
    static newLine() {
        console.log();
    }
    static table(data) {
        const maxKeyLength = Math.max(...data.map(item => item.key.length));
        data.forEach(item => {
            const paddedKey = item.key.padEnd(maxKeyLength);
            console.log(`  ${chalk.cyan(paddedKey)} ${item.value}`);
        });
    }
    static list(items, bullet = '•') {
        items.forEach(item => {
            console.log(`  ${chalk.cyan(bullet)} ${item}`);
        });
    }
}
