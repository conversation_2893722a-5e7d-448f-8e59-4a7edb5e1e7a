/**
 * Project creation utilities
 */
import type { ProjectOptions, ValidationResult } from '../types.js';
export declare class ProjectCreator {
    private templateManager;
    constructor();
    /**
     * Validate project name
     */
    validateProjectName(name: string): ValidationResult;
    /**
     * Detect available package manager
     */
    detectPackageManager(): 'pnpm' | 'yarn' | 'npm';
    /**
     * Copy template files to target directory
     */
    private copyTemplate;
    /**
     * Update package.json with project name
     */
    private updatePackageJson;
    /**
     * Install dependencies
     */
    private installDependencies;
    /**
     * Show post-creation guidance
     */
    private showGuidance;
    /**
     * Create a new project
     */
    createProject(options: ProjectOptions): Promise<boolean>;
}
