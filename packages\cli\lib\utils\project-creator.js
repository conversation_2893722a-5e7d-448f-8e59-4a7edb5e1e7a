/**
 * Project creation utilities
 */
import { existsSync, mkdirSync, readFileSync, writeFileSync } from 'fs';
import { copy } from 'fs-extra';
import { join, resolve } from 'path';
import { execSync } from 'child_process';
import ora from 'ora';
import { Logger } from './logger.js';
import { TemplateManager } from './template-manager.js';
export class ProjectCreator {
    templateManager;
    constructor() {
        this.templateManager = new TemplateManager();
    }
    /**
     * Validate project name
     */
    validateProjectName(name) {
        if (!name) {
            return { valid: false, error: 'Project name is required' };
        }
        // Check for valid npm package name format
        const npmNameRegex = /^[a-z0-9]([a-z0-9\-_])*$/;
        if (!npmNameRegex.test(name)) {
            return {
                valid: false,
                error: 'Project name must be lowercase and contain only letters, numbers, hyphens, and underscores'
            };
        }
        // Check if directory already exists
        const targetPath = resolve(process.cwd(), name);
        if (existsSync(targetPath)) {
            return {
                valid: false,
                error: `Directory '${name}' already exists`
            };
        }
        return { valid: true };
    }
    /**
     * Detect available package manager
     */
    detectPackageManager() {
        try {
            execSync('pnpm --version', { stdio: 'ignore' });
            return 'pnpm';
        }
        catch {
            try {
                execSync('yarn --version', { stdio: 'ignore' });
                return 'yarn';
            }
            catch {
                return 'npm';
            }
        }
    }
    /**
     * Copy template files to target directory
     */
    async copyTemplate(templatePath, targetPath) {
        const spinner = ora('Copying template files...').start();
        try {
            // Create target directory if it doesn't exist
            if (!existsSync(targetPath)) {
                mkdirSync(targetPath, { recursive: true });
            }
            // Copy all files from template to target, excluding certain directories
            await copy(templatePath, targetPath, {
                filter: (src) => {
                    const relativePath = src.replace(templatePath, '');
                    return !relativePath.includes('node_modules') &&
                        !relativePath.includes('dist') &&
                        !relativePath.includes('.git') &&
                        !relativePath.includes('pnpm-lock.yaml') &&
                        !relativePath.includes('yarn.lock') &&
                        !relativePath.includes('package-lock.json');
                }
            });
            spinner.succeed('Template files copied successfully');
            return true;
        }
        catch (error) {
            spinner.fail(`Failed to copy template: ${error instanceof Error ? error.message : 'Unknown error'}`);
            return false;
        }
    }
    /**
     * Update package.json with project name
     */
    updatePackageJson(projectPath, projectName) {
        const packageJsonPath = join(projectPath, 'package.json');
        try {
            Logger.step('Updating package.json...');
            const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));
            packageJson.name = projectName;
            writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
            Logger.success('package.json updated');
            return true;
        }
        catch (error) {
            Logger.error(`Failed to update package.json: ${error instanceof Error ? error.message : 'Unknown error'}`);
            return false;
        }
    }
    /**
     * Install dependencies
     */
    async installDependencies(projectPath, packageManager) {
        const spinner = ora(`Installing dependencies with ${packageManager}...`).start();
        try {
            const originalCwd = process.cwd();
            process.chdir(projectPath);
            const installCommand = packageManager === 'yarn' ? 'yarn install' : `${packageManager} install`;
            execSync(installCommand, { stdio: 'pipe' });
            process.chdir(originalCwd);
            spinner.succeed('Dependencies installed successfully');
            return true;
        }
        catch (error) {
            spinner.fail(`Failed to install dependencies: ${error instanceof Error ? error.message : 'Unknown error'}`);
            return false;
        }
    }
    /**
     * Show post-creation guidance
     */
    showGuidance(projectName, packageManager) {
        Logger.title('🎉 Project created successfully!');
        Logger.subtitle('Next steps:');
        Logger.list([
            `cd ${projectName}`,
            `${packageManager} run dev`,
            'Open http://localhost:3000 in your browser'
        ]);
        Logger.newLine();
        Logger.subtitle('Useful commands:');
        Logger.table([
            { key: `${packageManager} run build`, value: 'Build for production' },
            { key: `${packageManager} run lint`, value: 'Run ESLint' },
            { key: `${packageManager} run preview`, value: 'Preview production build' }
        ]);
        Logger.newLine();
        Logger.subtitle('Documentation:');
        Logger.dim('Check the README.md file in your project for more information');
        Logger.newLine();
    }
    /**
     * Create a new project
     */
    async createProject(options) {
        Logger.info(`Creating new project '${options.name}' from template '${options.template}'...`);
        // Validate template
        const templateValidation = this.templateManager.validateTemplate(options.template);
        if (!templateValidation.valid) {
            Logger.error(templateValidation.error || 'Template validation failed');
            return false;
        }
        // Validate project name
        const nameValidation = this.validateProjectName(options.name);
        if (!nameValidation.valid) {
            Logger.error(nameValidation.error || 'Project name validation failed');
            return false;
        }
        // Get template
        const template = this.templateManager.getTemplate(options.template);
        if (!template) {
            Logger.error(`Template '${options.template}' not found`);
            return false;
        }
        // Resolve target directory
        const projectPath = resolve(process.cwd(), options.directory || options.name);
        // Copy template
        if (!await this.copyTemplate(template.path, projectPath)) {
            return false;
        }
        // Update package.json
        if (!this.updatePackageJson(projectPath, options.name)) {
            return false;
        }
        // Install dependencies if not skipped
        if (!options.skipInstall) {
            const packageManager = options.packageManager || this.detectPackageManager();
            const installSuccess = await this.installDependencies(projectPath, packageManager);
            if (!installSuccess) {
                Logger.warning('Dependencies installation failed, you can install them manually later');
            }
        }
        else {
            Logger.info('Skipping dependency installation');
        }
        // Show guidance
        const packageManager = options.packageManager || this.detectPackageManager();
        this.showGuidance(options.name, packageManager);
        return true;
    }
}
