/**
 * Template management utilities
 */
import type { TemplateMetadata, ValidationResult } from '../types.js';
export declare class TemplateManager {
    private templatesDir;
    constructor();
    /**
     * Get the templates directory path
     */
    getTemplatesDir(): string;
    /**
     * Get all available templates with metadata
     */
    getAvailableTemplates(): TemplateMetadata[];
    /**
     * Validate a specific template
     */
    validateTemplate(templateName: string): ValidationResult;
    /**
     * Get template by name
     */
    getTemplate(templateName: string): TemplateMetadata | null;
    /**
     * Check if template exists
     */
    templateExists(templateName: string): boolean;
    /**
     * Get default template name
     */
    getDefaultTemplate(): string;
    /**
     * List templates in a formatted way
     */
    listTemplates(): void;
}
