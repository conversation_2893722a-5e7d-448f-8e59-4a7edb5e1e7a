{"name": "@cscs-agent/cli", "version": "0.3.0", "description": "CLI tool for creating CSCS Agent projects from templates", "private": true, "type": "module", "files": ["bin", "lib", "templates", "README.md"], "bin": "./bin/cli.js", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "node bin/cli.js --help", "lint": "eslint src --ext .ts,.js --fix"}, "dependencies": {"chalk": "^5.3.0", "commander": "^12.1.0", "inquirer": "^12.1.0", "ora": "^8.1.1", "fs-extra": "^11.2.0"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/node": "^22.15.0", "eslint": "^9.25.1", "typescript": "^5.8.2"}, "keywords": ["cli", "template", "cscs-agent", "project-generator"], "author": "CSCS Team", "license": "ISC", "packageManager": "pnpm@10.9.0", "engines": {"node": ">=18.0.0"}}