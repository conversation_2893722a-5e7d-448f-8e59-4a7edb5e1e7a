/**
 * Enhanced logging utilities with colors and formatting
 */

import chalk from 'chalk';

export class Logger {
  static info(message: string): void {
    console.log(chalk.blue('ℹ'), message);
  }

  static success(message: string): void {
    console.log(chalk.green('✓'), message);
  }

  static warning(message: string): void {
    console.log(chalk.yellow('⚠'), message);
  }

  static error(message: string): void {
    console.error(chalk.red('✗'), message);
  }

  static step(message: string): void {
    console.log(chalk.cyan('→'), message);
  }

  static title(message: string): void {
    console.log('\n' + chalk.bold.cyan(message) + '\n');
  }

  static subtitle(message: string): void {
    console.log(chalk.bold(message));
  }

  static dim(message: string): void {
    console.log(chalk.dim(message));
  }

  static newLine(): void {
    console.log();
  }

  static table(data: Array<{ key: string; value: string }>): void {
    const maxKeyLength = Math.max(...data.map(item => item.key.length));
    
    data.forEach(item => {
      const paddedKey = item.key.padEnd(maxKeyLength);
      console.log(`  ${chalk.cyan(paddedKey)} ${item.value}`);
    });
  }

  static list(items: string[], bullet: string = '•'): void {
    items.forEach(item => {
      console.log(`  ${chalk.cyan(bullet)} ${item}`);
    });
  }
}
