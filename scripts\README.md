# CSCS Agent Project Creator

A comprehensive script suite for creating CSCS Agent projects using templates. This toolset provides both CLI and wrapper scripts for cross-platform project initialization.

## Overview

The project creator consists of three main components:

1. **CLI Tool** (`packages/cli/bin/create.js`) - Core CLI functionality
2. **Node.js Wrapper** (`scripts/create-project.js`) - Enhanced wrapper with additional features
3. **Platform Scripts** (`scripts/create-project.sh` & `scripts/create-project.bat`) - Cross-platform shell wrappers

## Features

- ✅ **Template-based project creation** - Initialize projects from predefined templates
- ✅ **Cross-platform compatibility** - Works on Windows, macOS, and Linux
- ✅ **Automatic dependency detection** - Detects and uses pnpm, yarn, or npm
- ✅ **Template validation** - Validates templates before use
- ✅ **Enhanced error handling** - Comprehensive error reporting and recovery
- ✅ **Progress logging** - Clear progress indicators and status messages
- ✅ **Interactive template selection** - Lists available templates with descriptions
- ✅ **Project name validation** - Ensures valid npm package names
- ✅ **Post-creation guidance** - Provides next steps after project creation

## Installation

### Prerequisites

- Node.js (>= 22.15.0)
- npm, yarn, or pnpm
- Git (optional, for cloning templates)

### Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd agent
```

2. Install dependencies:
```bash
pnpm install
```

3. Build the CLI tool:
```bash
pnpm run build:packages
```

## Usage

### Method 1: Using the CLI Tool Directly

```bash
# Create a project with default template
npx @cscs-agent/cli --create my-project

# Create a project with specific template
npx @cscs-agent/cli --create --template basic --name my-project

# List available templates
npx @cscs-agent/cli --list

# Show help
npx @cscs-agent/cli --help
```

### Method 2: Using the Node.js Wrapper (Recommended)

```bash
# Create a project
node scripts/create-project.js my-project

# Create with specific template
node scripts/create-project.js --name my-project --template basic

# List available templates
node scripts/create-project.js --list

# Show help
node scripts/create-project.js --help
```

### Method 3: Using Platform-Specific Scripts

#### On Unix/Linux/macOS:
```bash
# Make executable (first time only)
chmod +x scripts/create-project.sh

# Create a project
./scripts/create-project.sh my-project

# List templates
./scripts/create-project.sh --list
```

#### On Windows:
```cmd
# Create a project
scripts\create-project.bat my-project

# List templates
scripts\create-project.bat --list
```

## Command Line Options

| Option | Short | Description | Example |
|--------|-------|-------------|---------|
| `--create` | - | Create a new project (CLI only) | `--create` |
| `--name` | `-n` | Project name | `--name my-app` |
| `--template` | `-t` | Template to use | `--template basic` |
| `--dir` | `-d` | Target directory | `--dir ./projects/my-app` |
| `--list` | `-l` | List available templates | `--list` |
| `--help` | `-h` | Show help message | `--help` |

## Templates

Templates are located in the `packages/cli/templates/` directory. Each template should:

1. Be a valid directory containing a complete project structure
2. Include a `package.json` file with project metadata
3. Follow the established patterns for CSCS Agent projects

### Available Templates

- **basic** - A basic CSCS Agent application with essential features

### Template Structure

```
packages/cli/templates/
├── basic/
│   ├── package.json          # Project metadata
│   ├── index.html            # Main HTML file
│   ├── vite.config.js        # Vite configuration
│   ├── tsconfig.json         # TypeScript configuration
│   ├── src/
│   │   ├── main.tsx          # Application entry point
│   │   ├── agent-config.tsx  # Agent configuration
│   │   └── pages/            # Application pages
│   └── public/               # Static assets
```

## Creating Custom Templates

1. Create a new directory in `packages/cli/templates/`
2. Add a complete project structure
3. Ensure `package.json` exists with proper metadata
4. Test the template with the creation scripts

Example `package.json` for a template:
```json
{
  "name": "my-template",
  "version": "1.0.0",
  "description": "My custom CSCS Agent template",
  "private": true,
  "type": "module",
  "scripts": {
    "dev": "vite --force",
    "build": "vite build",
    "lint": "eslint ./src --ext .ts,.tsx --fix",
    "preview": "vite preview"
  },
  "dependencies": {
    // ... your dependencies
  }
}
```

## Error Handling

The scripts include comprehensive error handling for common scenarios:

- **Missing Node.js/npm** - Checks for required tools
- **Invalid project names** - Validates npm package name format
- **Existing directories** - Prevents overwriting existing projects
- **Missing templates** - Validates template availability
- **Network issues** - Handles dependency installation failures
- **Permission errors** - Provides guidance for permission issues

## Troubleshooting

### Common Issues

1. **"Templates directory not found"**
   - Ensure you're running the script from the project root
   - Check that `packages/cli/templates/` exists

2. **"CLI tool not found"**
   - Run `pnpm run build:packages` to build the CLI tool
   - Ensure the CLI package is properly configured

3. **"Permission denied"**
   - On Unix systems, make scripts executable: `chmod +x scripts/create-project.sh`
   - On Windows, run as administrator if needed

4. **"Project name validation failed"**
   - Use lowercase letters, numbers, hyphens, and underscores only
   - Ensure the name doesn't start with a number or special character

### Debug Mode

For additional debugging information, you can:

1. Check Node.js and npm versions:
```bash
node --version
npm --version
```

2. Verify template availability:
```bash
node scripts/create-project.js --list
```

3. Test CLI tool directly:
```bash
npx @cscs-agent/cli --help
```

## Development

### Project Structure

```
scripts/
├── create-project.js     # Main Node.js wrapper script
├── create-project.sh     # Unix shell wrapper
├── create-project.bat    # Windows batch wrapper
└── README.md            # This documentation

packages/cli/
├── bin/create.js        # Core CLI implementation
├── templates/           # Project templates
└── package.json         # CLI package configuration
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test with multiple templates and platforms
5. Submit a pull request

### Testing

Test the scripts on different platforms:

```bash
# Test template listing
node scripts/create-project.js --list

# Test project creation
node scripts/create-project.js test-project

# Test with different templates
node scripts/create-project.js --template basic --name test-basic

# Clean up test projects
rm -rf test-project test-basic
```

## License

This project is licensed under the ISC License. See the main project LICENSE file for details.
