@echo off
REM CSCS Agent Project Creator - Windows Batch Script
REM This script provides a Windows-compatible wrapper for creating CSCS Agent projects

setlocal enabledelayedexpansion

REM Set script directory
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."

REM Colors for Windows (limited support)
set "COLOR_RESET="
set "COLOR_GREEN=[92m"
set "COLOR_RED=[91m"
set "COLOR_YELLOW=[93m"
set "COLOR_BLUE=[94m"
set "COLOR_CYAN=[96m"

REM Check if Node.js is installed
echo %COLOR_BLUE%Checking Node.js installation...%COLOR_RESET%
node --version >nul 2>&1
if errorlevel 1 (
    echo %COLOR_RED%Error: Node.js is not installed or not in PATH%COLOR_RESET%
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm is available
npm --version >nul 2>&1
if errorlevel 1 (
    echo %COLOR_RED%Error: npm is not available%COLOR_RESET%
    pause
    exit /b 1
)

echo %COLOR_GREEN%Node.js and npm are available%COLOR_RESET%

REM Change to project root directory
cd /d "%PROJECT_ROOT%"

REM Check if the Node.js script exists
if not exist "scripts\create-project.js" (
    echo %COLOR_RED%Error: create-project.js not found%COLOR_RESET%
    echo Please ensure you're running this script from the correct directory
    pause
    exit /b 1
)

REM Show help if no arguments provided
if "%~1"=="" (
    echo.
    echo %COLOR_CYAN%CSCS Agent Project Creator%COLOR_RESET%
    echo.
    echo Usage:
    echo   create-project.bat [project-name]
    echo   create-project.bat --help
    echo   create-project.bat --list
    echo.
    echo Examples:
    echo   create-project.bat my-agent-app
    echo   create-project.bat --list
    echo.
    pause
    exit /b 0
)

REM Handle help flag
if "%~1"=="--help" (
    node scripts\create-project.js --help
    pause
    exit /b 0
)

REM Handle list flag
if "%~1"=="--list" (
    node scripts\create-project.js --list
    pause
    exit /b 0
)

REM Execute the Node.js script with all arguments
echo %COLOR_CYAN%Starting project creation...%COLOR_RESET%
echo.

node scripts\create-project.js %*

REM Check exit code
if errorlevel 1 (
    echo.
    echo %COLOR_RED%Project creation failed%COLOR_RESET%
    pause
    exit /b 1
) else (
    echo.
    echo %COLOR_GREEN%Project creation completed successfully!%COLOR_RESET%
    pause
    exit /b 0
)
