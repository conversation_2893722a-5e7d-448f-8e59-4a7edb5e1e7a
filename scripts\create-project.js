#!/usr/bin/env node

/**
 * CSCS Agent Project Creator
 * 
 * A cross-platform wrapper script for creating CSCS Agent projects using templates.
 * This script provides enhanced functionality over the basic CLI tool including:
 * - Interactive template selection
 * - Enhanced error handling and logging
 * - Cross-platform compatibility
 * - Progress indicators
 * - Post-creation setup guidance
 */

import { execSync, spawn } from 'child_process';
import { existsSync, readdirSync, readFileSync, statSync } from 'fs';
import { join, resolve } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Enhanced logging utilities
const logger = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.error(`${colors.red}✗${colors.reset} ${msg}`),
  step: (msg) => console.log(`${colors.cyan}→${colors.reset} ${msg}`),
  title: (msg) => console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}\n`),
  subtitle: (msg) => console.log(`${colors.bright}${msg}${colors.reset}`),
  dim: (msg) => console.log(`${colors.dim}${msg}${colors.reset}`),
};

// Platform detection
const isWindows = process.platform === 'win32';
const isMacOS = process.platform === 'darwin';
const isLinux = process.platform === 'linux';

// Get project root directory
const getProjectRoot = () => {
  return resolve(__dirname, '..');
};

// Get templates directory (check both locations)
const getTemplatesDir = () => {
  const projectRoot = getProjectRoot();
  
  // Check packages/cli/templates first
  const cliTemplatesDir = join(projectRoot, 'packages', 'cli', 'templates');
  if (existsSync(cliTemplatesDir)) {
    return cliTemplatesDir;
  }
  
  // Fallback to template directory
  const templateDir = join(projectRoot, 'template');
  if (existsSync(templateDir)) {
    return templateDir;
  }
  
  return null;
};

// Get available templates with metadata
const getAvailableTemplates = () => {
  const templatesDir = getTemplatesDir();
  
  if (!templatesDir) {
    logger.error('Templates directory not found');
    return [];
  }

  try {
    const templates = readdirSync(templatesDir).filter(item => {
      const itemPath = join(templatesDir, item);
      return statSync(itemPath).isDirectory();
    });

    return templates.map(template => {
      const templatePath = join(templatesDir, template);
      const packageJsonPath = join(templatePath, 'package.json');
      
      let metadata = {
        name: template,
        description: '',
        version: '1.0.0',
        valid: false,
      };

      if (existsSync(packageJsonPath)) {
        try {
          const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));
          metadata = {
            name: template,
            description: packageJson.description || '',
            version: packageJson.version || '1.0.0',
            valid: true,
          };
        } catch (error) {
          logger.warning(`Failed to read package.json for template '${template}': ${error.message}`);
        }
      }

      return metadata;
    });
  } catch (error) {
    logger.error(`Failed to read templates directory: ${error.message}`);
    return [];
  }
};

// Check if CLI tool is available
const checkCliAvailability = () => {
  try {
    // Check if we can run the CLI tool
    execSync('npx @cscs-agent/cli --help', { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
};

// Install CLI tool if not available
const ensureCliTool = async () => {
  logger.step('Checking CLI tool availability...');
  
  if (checkCliAvailability()) {
    logger.success('CLI tool is available');
    return true;
  }

  logger.warning('CLI tool not found, attempting to install...');
  
  try {
    // Try to install the CLI tool
    execSync('npm install -g @cscs-agent/cli', { stdio: 'inherit' });
    
    if (checkCliAvailability()) {
      logger.success('CLI tool installed successfully');
      return true;
    } else {
      logger.error('CLI tool installation failed');
      return false;
    }
  } catch (error) {
    logger.error(`Failed to install CLI tool: ${error.message}`);
    return false;
  }
};

// Interactive template selection
const selectTemplate = (templates) => {
  if (templates.length === 0) {
    logger.error('No templates available');
    return null;
  }

  if (templates.length === 1) {
    logger.info(`Using template: ${templates[0].name}`);
    return templates[0].name;
  }

  logger.subtitle('Available templates:');
  templates.forEach((template, index) => {
    const status = template.valid ? colors.green + '✓' : colors.red + '✗';
    const description = template.description ? ` - ${template.description}` : '';
    console.log(`  ${index + 1}. ${status}${colors.reset} ${colors.bright}${template.name}${colors.reset}${description}`);
  });

  // For now, return the first valid template
  // In a real implementation, you might want to use a library like 'inquirer' for interactive selection
  const validTemplates = templates.filter(t => t.valid);
  if (validTemplates.length > 0) {
    logger.info(`Auto-selecting template: ${validTemplates[0].name}`);
    return validTemplates[0].name;
  }

  return null;
};

// Validate project name
const validateProjectName = (name) => {
  if (!name) {
    return { valid: false, error: 'Project name is required' };
  }

  // Check for valid npm package name format
  const npmNameRegex = /^[a-z0-9]([a-z0-9\-_])*$/;
  if (!npmNameRegex.test(name)) {
    return { 
      valid: false, 
      error: 'Project name must be lowercase and contain only letters, numbers, hyphens, and underscores' 
    };
  }

  // Check if directory already exists
  const targetPath = resolve(process.cwd(), name);
  if (existsSync(targetPath)) {
    return { 
      valid: false, 
      error: `Directory '${name}' already exists` 
    };
  }

  return { valid: true };
};

// Execute CLI command with enhanced error handling
const executeCliCommand = (templateName, projectName, targetDir) => {
  return new Promise((resolve, reject) => {
    const args = ['@cscs-agent/cli', '--create', '--template', templateName, '--name', projectName];
    
    if (targetDir) {
      args.push('--dir', targetDir);
    }

    logger.step(`Executing: npx ${args.join(' ')}`);

    const child = spawn('npx', args, {
      stdio: 'inherit',
      shell: isWindows,
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`CLI command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
};

// Show post-creation guidance
const showGuidance = (projectName) => {
  logger.title('🎉 Project created successfully!');
  
  logger.subtitle('Next steps:');
  console.log(`  1. ${colors.cyan}cd ${projectName}${colors.reset}`);
  console.log(`  2. ${colors.cyan}npm run dev${colors.reset} or ${colors.cyan}pnpm dev${colors.reset}`);
  console.log(`  3. Open ${colors.cyan}http://localhost:3000${colors.reset} in your browser`);
  
  logger.subtitle('\nUseful commands:');
  console.log(`  ${colors.cyan}npm run build${colors.reset}     - Build for production`);
  console.log(`  ${colors.cyan}npm run lint${colors.reset}      - Run ESLint`);
  console.log(`  ${colors.cyan}npm run preview${colors.reset}   - Preview production build`);
  
  logger.subtitle('\nDocumentation:');
  logger.dim('  Check the README.md file in your project for more information');
  
  console.log();
};

// Main create project function
const createProject = async (options = {}) => {
  try {
    logger.title('CSCS Agent Project Creator');
    
    // Get available templates
    const templates = getAvailableTemplates();
    if (templates.length === 0) {
      logger.error('No templates found. Please ensure templates are available in the templates directory.');
      process.exit(1);
    }

    // Select template
    const templateName = options.template || selectTemplate(templates);
    if (!templateName) {
      logger.error('No valid template selected');
      process.exit(1);
    }

    // Validate project name
    const projectName = options.name;
    if (!projectName) {
      logger.error('Project name is required');
      process.exit(1);
    }

    const nameValidation = validateProjectName(projectName);
    if (!nameValidation.valid) {
      logger.error(nameValidation.error);
      process.exit(1);
    }

    // Ensure CLI tool is available
    const cliAvailable = await ensureCliTool();
    if (!cliAvailable) {
      logger.error('CLI tool is not available and could not be installed');
      process.exit(1);
    }

    // Execute CLI command
    logger.step(`Creating project '${projectName}' using template '${templateName}'...`);
    await executeCliCommand(templateName, projectName, options.dir);

    // Show guidance
    showGuidance(projectName);

  } catch (error) {
    logger.error(`Failed to create project: ${error.message}`);
    process.exit(1);
  }
};

// Parse command line arguments
const parseArgs = () => {
  const args = process.argv.slice(2);
  const options = {
    name: null,
    template: null,
    dir: null,
    help: false,
    list: false,
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--template':
      case '-t':
        options.template = args[++i];
        break;
      case '--name':
      case '-n':
        options.name = args[++i];
        break;
      case '--dir':
      case '-d':
        options.dir = args[++i];
        break;
      case '--list':
      case '-l':
        options.list = true;
        break;
      case '--help':
      case '-h':
        options.help = true;
        break;
      default:
        if (!options.name && !arg.startsWith('-')) {
          options.name = arg;
        }
        break;
    }
  }

  return options;
};

// Show help
const showHelp = () => {
  console.log(`
${colors.bright}CSCS Agent Project Creator${colors.reset}

${colors.bright}Usage:${colors.reset}
  node scripts/create-project.js [project-name]
  node scripts/create-project.js --name <project-name> --template <template>

${colors.bright}Options:${colors.reset}
  --name, -n            Project name
  --template, -t        Template to use
  --dir, -d             Target directory (default: project name)
  --list, -l            List available templates
  --help, -h            Show this help message

${colors.bright}Examples:${colors.reset}
  node scripts/create-project.js my-agent-app
  node scripts/create-project.js --name my-app --template basic
  node scripts/create-project.js --list
`);
};

// List templates
const listTemplates = () => {
  const templates = getAvailableTemplates();
  
  if (templates.length === 0) {
    logger.warning('No templates found');
    return;
  }

  logger.title('Available Templates');
  
  templates.forEach(template => {
    const status = template.valid ? colors.green + '✓' : colors.red + '✗';
    const description = template.description ? ` - ${template.description}` : '';
    console.log(`  ${status}${colors.reset} ${colors.bright}${template.name}${colors.reset}${description}`);
    if (template.version) {
      console.log(`    ${colors.dim}Version: ${template.version}${colors.reset}`);
    }
  });
  console.log();
};

// Main execution
const main = async () => {
  const options = parseArgs();

  if (options.help) {
    showHelp();
    return;
  }

  if (options.list) {
    listTemplates();
    return;
  }

  await createProject(options);
};

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    logger.error(`Unexpected error: ${error.message}`);
    process.exit(1);
  });
}

export { createProject, getAvailableTemplates, validateProjectName };
