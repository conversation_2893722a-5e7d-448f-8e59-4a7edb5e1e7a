#!/bin/bash

# CSCS Agent Project Creator - Unix Shell Script
# This script provides a Unix-compatible wrapper for creating CSCS Agent projects

set -e  # Exit on any error

# Colors for terminal output
COLOR_RESET='\033[0m'
COLOR_GREEN='\033[0;32m'
COLOR_RED='\033[0;31m'
COLOR_YELLOW='\033[0;33m'
COLOR_BLUE='\033[0;34m'
COLOR_CYAN='\033[0;36m'
COLOR_BOLD='\033[1m'

# Logging functions
log_info() {
    echo -e "${COLOR_BLUE}ℹ${COLOR_RESET} $1"
}

log_success() {
    echo -e "${COLOR_GREEN}✓${COLOR_RESET} $1"
}

log_warning() {
    echo -e "${COLOR_YELLOW}⚠${COLOR_RESET} $1"
}

log_error() {
    echo -e "${COLOR_RED}✗${COLOR_RESET} $1"
}

log_title() {
    echo -e "\n${COLOR_BOLD}${COLOR_CYAN}$1${COLOR_RESET}\n"
}

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Check if Node.js is installed
check_nodejs() {
    log_info "Checking Node.js installation..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed or not in PATH"
        echo "Please install Node.js from https://nodejs.org/"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm is not available"
        exit 1
    fi
    
    log_success "Node.js and npm are available"
    
    # Show versions
    NODE_VERSION=$(node --version)
    NPM_VERSION=$(npm --version)
    log_info "Node.js version: $NODE_VERSION"
    log_info "npm version: $NPM_VERSION"
}

# Check if the Node.js script exists
check_script() {
    if [ ! -f "$PROJECT_ROOT/scripts/create-project.js" ]; then
        log_error "create-project.js not found"
        echo "Please ensure you're running this script from the correct directory"
        exit 1
    fi
}

# Show help
show_help() {
    echo -e "\n${COLOR_BOLD}${COLOR_CYAN}CSCS Agent Project Creator${COLOR_RESET}\n"
    echo "Usage:"
    echo "  ./create-project.sh [project-name]"
    echo "  ./create-project.sh --help"
    echo "  ./create-project.sh --list"
    echo ""
    echo "Examples:"
    echo "  ./create-project.sh my-agent-app"
    echo "  ./create-project.sh --list"
    echo ""
}

# Make script executable if it isn't already
make_executable() {
    if [ ! -x "$0" ]; then
        log_info "Making script executable..."
        chmod +x "$0"
        log_success "Script is now executable"
    fi
}

# Main execution
main() {
    # Make script executable
    make_executable
    
    # Change to project root directory
    cd "$PROJECT_ROOT"
    
    # Check prerequisites
    check_nodejs
    check_script
    
    # Handle arguments
    if [ $# -eq 0 ]; then
        show_help
        exit 0
    fi
    
    case "$1" in
        --help|-h)
            node scripts/create-project.js --help
            exit 0
            ;;
        --list|-l)
            node scripts/create-project.js --list
            exit 0
            ;;
        *)
            # Execute the Node.js script with all arguments
            log_title "Starting project creation..."
            
            if node scripts/create-project.js "$@"; then
                log_success "Project creation completed successfully!"
            else
                log_error "Project creation failed"
                exit 1
            fi
            ;;
    esac
}

# Error handling
trap 'log_error "Script interrupted"; exit 1' INT TERM

# Run main function
main "$@"
