#!/usr/bin/env node

/**
 * CSCS Agent Project Creator - Usage Examples
 * 
 * This file demonstrates various ways to use the project creation scripts
 * and provides examples for different scenarios.
 */

import { execSync } from 'child_process';
import { existsSync, rmSync } from 'fs';
import { resolve } from 'path';

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.error(`${colors.red}✗${colors.reset} ${msg}`),
  title: (msg) => console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}\n`),
};

// Helper function to run commands safely
const runCommand = (command, description) => {
  try {
    log.info(`${description}...`);
    console.log(`${colors.cyan}$ ${command}${colors.reset}`);
    
    const output = execSync(command, { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    if (output.trim()) {
      console.log(output);
    }
    
    log.success(`${description} completed`);
    return true;
  } catch (error) {
    log.error(`${description} failed: ${error.message}`);
    return false;
  }
};

// Clean up test projects
const cleanup = (projectNames) => {
  log.info('Cleaning up test projects...');
  
  projectNames.forEach(name => {
    const projectPath = resolve(process.cwd(), name);
    if (existsSync(projectPath)) {
      try {
        rmSync(projectPath, { recursive: true, force: true });
        log.success(`Removed ${name}`);
      } catch (error) {
        log.warning(`Failed to remove ${name}: ${error.message}`);
      }
    }
  });
};

// Example 1: Basic project creation
const example1 = () => {
  log.title('Example 1: Basic Project Creation');
  
  const projectName = 'example-basic-app';
  
  log.info('This example creates a basic CSCS Agent project using the default template');
  
  // Clean up if exists
  cleanup([projectName]);
  
  // Create project using Node.js wrapper
  const success = runCommand(
    `node scripts/create-project.js ${projectName}`,
    'Creating basic project'
  );
  
  if (success) {
    log.success(`Project '${projectName}' created successfully!`);
    log.info(`You can now run: cd ${projectName} && npm run dev`);
  }
  
  return success;
};

// Example 2: Project creation with specific template
const example2 = () => {
  log.title('Example 2: Project Creation with Specific Template');
  
  const projectName = 'example-template-app';
  
  log.info('This example creates a project using a specific template');
  
  // Clean up if exists
  cleanup([projectName]);
  
  // Create project with specific template
  const success = runCommand(
    `node scripts/create-project.js --name ${projectName} --template basic`,
    'Creating project with specific template'
  );
  
  if (success) {
    log.success(`Project '${projectName}' created with template 'basic'!`);
  }
  
  return success;
};

// Example 3: List available templates
const example3 = () => {
  log.title('Example 3: List Available Templates');
  
  log.info('This example shows how to list all available templates');
  
  // List templates using Node.js wrapper
  const success = runCommand(
    'node scripts/create-project.js --list',
    'Listing available templates'
  );
  
  return success;
};

// Example 4: Using CLI tool directly
const example4 = () => {
  log.title('Example 4: Using CLI Tool Directly');
  
  const projectName = 'example-cli-app';
  
  log.info('This example uses the CLI tool directly via npx');
  
  // Clean up if exists
  cleanup([projectName]);
  
  // Create project using CLI directly
  const success = runCommand(
    `npx @cscs-agent/cli --create --name ${projectName} --template basic`,
    'Creating project with CLI tool'
  );
  
  if (success) {
    log.success(`Project '${projectName}' created using CLI tool!`);
  }
  
  return success;
};

// Example 5: Error handling demonstration
const example5 = () => {
  log.title('Example 5: Error Handling Demonstration');
  
  log.info('This example demonstrates error handling for various scenarios');
  
  // Try to create project with invalid name
  log.info('Testing invalid project name...');
  runCommand(
    'node scripts/create-project.js "Invalid Project Name!"',
    'Creating project with invalid name (should fail)'
  );
  
  // Try to create project with non-existent template
  log.info('Testing non-existent template...');
  runCommand(
    'node scripts/create-project.js --name test-app --template non-existent',
    'Creating project with non-existent template (should fail)'
  );
  
  return true;
};

// Example 6: Cross-platform script usage
const example6 = () => {
  log.title('Example 6: Cross-Platform Script Usage');
  
  log.info('This example shows how to use platform-specific scripts');
  
  const isWindows = process.platform === 'win32';
  
  if (isWindows) {
    log.info('Running on Windows - using batch script');
    runCommand(
      'scripts\\create-project.bat --list',
      'Listing templates using Windows batch script'
    );
  } else {
    log.info('Running on Unix/Linux/macOS - using shell script');
    runCommand(
      './scripts/create-project.sh --list',
      'Listing templates using shell script'
    );
  }
  
  return true;
};

// Main function to run all examples
const runAllExamples = async () => {
  log.title('CSCS Agent Project Creator - Usage Examples');
  
  log.info('This script demonstrates various ways to use the project creation tools');
  log.warning('Note: This will create and clean up test projects in the current directory');
  
  const examples = [
    { name: 'Basic Project Creation', fn: example1 },
    { name: 'Specific Template', fn: example2 },
    { name: 'List Templates', fn: example3 },
    { name: 'CLI Tool Direct', fn: example4 },
    { name: 'Error Handling', fn: example5 },
    { name: 'Cross-Platform Scripts', fn: example6 },
  ];
  
  const results = [];
  
  for (const example of examples) {
    try {
      const success = example.fn();
      results.push({ name: example.name, success });
    } catch (error) {
      log.error(`Example '${example.name}' failed: ${error.message}`);
      results.push({ name: example.name, success: false });
    }
    
    // Add some spacing between examples
    console.log();
  }
  
  // Summary
  log.title('Examples Summary');
  
  results.forEach(result => {
    const status = result.success ? colors.green + '✓' : colors.red + '✗';
    console.log(`  ${status}${colors.reset} ${result.name}`);
  });
  
  // Clean up all test projects
  log.title('Cleanup');
  cleanup([
    'example-basic-app',
    'example-template-app', 
    'example-cli-app',
    'test-app'
  ]);
  
  log.success('All examples completed!');
};

// Run specific example or all examples
const main = () => {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    runAllExamples();
    return;
  }
  
  const exampleNumber = parseInt(args[0]);
  
  switch (exampleNumber) {
    case 1:
      example1();
      break;
    case 2:
      example2();
      break;
    case 3:
      example3();
      break;
    case 4:
      example4();
      break;
    case 5:
      example5();
      break;
    case 6:
      example6();
      break;
    default:
      log.error('Invalid example number. Use 1-6 or run without arguments for all examples.');
      process.exit(1);
  }
};

// Show usage if help is requested
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
${colors.bright}CSCS Agent Project Creator - Examples${colors.reset}

Usage:
  node scripts/examples.js [example-number]

Examples:
  node scripts/examples.js     # Run all examples
  node scripts/examples.js 1   # Run example 1 only
  node scripts/examples.js 3   # Run example 3 only

Available examples:
  1. Basic Project Creation
  2. Project Creation with Specific Template  
  3. List Available Templates
  4. Using CLI Tool Directly
  5. Error Handling Demonstration
  6. Cross-Platform Script Usage
`);
  process.exit(0);
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
